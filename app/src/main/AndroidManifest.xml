<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/logo"
            android:usesCleartextTraffic="true"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/logo"
            android:supportsRtl="true"
            android:theme="@style/Theme.Android_jjx"
            tools:targetApi="31">
        <activity
                android:name=".activity.MainActivity"
                android:exported="true"
                android:theme="@style/Theme.Android_jjx">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>

                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <activity android:name=".activity.PackBoxActivity" />
        <activity android:name=".activity.ScanBoxCodeActivity" />
        <activity android:name=".activity.ScanBottleCodeActivity" />
    </application>

</manifest>