package com.example.jjx.util;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;

import androidx.annotation.StringRes;

/**
 * <AUTHOR>
 * @Description: 弹窗工具类，封装了常用的弹窗提示功能，适配Android 11及以上系统
 */
public class DialogUtil {

    /**
     * 显示一个简单的提示弹窗
     * 注意：必须传入Activity作为上下文，不能使用Application或其他Context
     *
     * @param activity 活动上下文（当前正在运行的Activity）
     * @param message  消息内容
     */
//    public static void showAlertDialog(Activity activity, String message) {
//        // 检查Activity是否有效（关键修复）
//        if (!isActivityAlive(activity)) {
//            return;
//        }
//        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
//        builder.setTitle("提示")
//                .setMessage(message)
//                .setPositiveButton("确定", (dialog, which) -> {
//                    dialog.dismiss();
//                })
//                .setCancelable(false);
//
//        AlertDialog dialog = builder.create();
//
//        // 显示前再次检查Activity状态（双重保险）
//        if (isActivityAlive(activity)) {
//            dialog.show();
//        }
//    }


//    DialogUtil.showAlertDialog(this, "操作成功", (dialog, which) -> {
//        // 处理点击事件
//        dialog.dismiss();
//    });

    /**
     * 显示一个简单的提示弹窗
     * 注意：必须传入Activity作为上下文，不能使用Application或其他Context
     *
     * @param activity 活动上下文（当前正在运行的Activity）
     * @param message  消息内容
     * @param listener 确认按钮点击事件
     */
    public static void showAlertDialog(Activity activity, String message, DialogInterface.OnClickListener listener) {
        // 检查Activity是否有效（关键修复）
        if (!isActivityAlive(activity)) {
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("提示")
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> {
                    if (listener != null) {
                        listener.onClick(dialog, which);
                    }
                    dialog.dismiss();
                })
                .setCancelable(false);

        AlertDialog dialog = builder.create();

        // 显示前再次检查Activity状态（双重保险）
        if (isActivityAlive(activity)) {
            dialog.show();
        }
    }

    /**
     * 显示一个简单的提示弹窗
     * 注意：必须传入Activity作为上下文，不能使用Application或其他Context
     *
     * @param activity  活动上下文（当前正在运行的Activity）
     * @param title     标题
     * @param message   消息内容
     * @param positive  确认按钮文字
     * @param listener  确认按钮点击事件
     */
    public static void showAlertDialog(Activity activity, String title, String message,
                                       String positive, DialogInterface.OnClickListener listener) {
        // 检查Activity是否有效（关键修复）
        if (!isActivityAlive(activity)) {
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(title)
                .setMessage(message)
                .setPositiveButton(positive, (dialog, which) -> {
                    if (listener != null) {
                        listener.onClick(dialog, which);
                    }
                    dialog.dismiss();
                })
                .setCancelable(false);

        AlertDialog dialog = builder.create();

        // 显示前再次检查Activity状态（双重保险）
        if (isActivityAlive(activity)) {
            dialog.show();
        }
    }

    /**
     * 重载方法，使用资源ID
     */
    public static void showAlertDialog(Activity activity, @StringRes int titleId, @StringRes int messageId,
                                       @StringRes int positiveId, DialogInterface.OnClickListener listener) {
        if (!isActivityAlive(activity)) {
            return;
        }

        showAlertDialog(activity, activity.getString(titleId), activity.getString(messageId),
                activity.getString(positiveId), listener);
    }

    /**
     * 显示一个确认弹窗（包含确认和取消按钮）
     */
    public static void showConfirmDialog(Activity activity, String title, String message,
                                         String positiveText, String negativeText,
                                         DialogInterface.OnClickListener positiveListener,
                                         DialogInterface.OnClickListener negativeListener) {
        if (!isActivityAlive(activity)) {
            return;
        }

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(title)
                .setMessage(message)
                .setPositiveButton(positiveText, (dialog, which) -> {
                    if (positiveListener != null) {
                        positiveListener.onClick(dialog, which);
                    }
                    dialog.dismiss();
                })
                .setNegativeButton(negativeText, (dialog, which) -> {
                    if (negativeListener != null) {
                        negativeListener.onClick(dialog, which);
                    }
                    dialog.dismiss();
                })
                .setCancelable(true);

        AlertDialog dialog = builder.create();
        if (isActivityAlive(activity)) {
            dialog.show();
        }
    }

    /**
     * 显示一个输入弹窗
     */
    public static void showInputDialog(Activity activity, String title, String hint,
                                       String positiveText, String negativeText,
                                       InputDialogListener inputListener,
                                       DialogInterface.OnClickListener negativeListener) {
        if (!isActivityAlive(activity)) {
            return;
        }

        EditText editText = new EditText(activity);
        editText.setHint(hint);

        int padding = dpToPx(activity, 16);
        editText.setPadding(padding, padding, padding, padding);

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(title)
                .setView(editText)
                .setPositiveButton(positiveText, (dialog, which) -> {
                    String inputText = editText.getText().toString().trim();
                    if (inputListener != null) {
                        inputListener.onInputComplete(inputText);
                    }
                    dialog.dismiss();
                })
                .setNegativeButton(negativeText, negativeListener)
                .setCancelable(true);

        AlertDialog dialog = builder.create();
        if (isActivityAlive(activity)) {
            dialog.show();
        }
    }

    /**
     * 显示一个自定义布局的弹窗
     */
    public static AlertDialog showCustomDialog(Activity activity, int layoutResId, String title,
                                               String positiveText, DialogInterface.OnClickListener listener) {
        if (!isActivityAlive(activity)) {
            return null;
        }

        View customView = LayoutInflater.from(activity).inflate(layoutResId, null);

        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle(title)
                .setView(customView)
                .setPositiveButton(positiveText, listener)
                .setNegativeButton("取消", (dialog, which) -> dialog.dismiss())
                .setCancelable(true);

        AlertDialog dialog = builder.create();
        if (isActivityAlive(activity)) {
            dialog.show();
        }
        return dialog;
    }

    /**
     * 检查Activity是否处于活动状态（未被销毁且未结束）
     * 这是解决BadTokenException的关键方法
     */
    private static boolean isActivityAlive(Activity activity) {
        if (activity == null) {
            return false;
        }
        // 检查Activity生命周期状态
        return !activity.isFinishing() && !activity.isDestroyed();
    }

    /**
     * dp转px
     */
    private static int dpToPx(Context context, float dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return (int) (dp * density + 0.5f);
    }

    /**
     * 输入弹窗回调接口
     */
    public interface InputDialogListener {
        void onInputComplete(String inputText);
    }
}
