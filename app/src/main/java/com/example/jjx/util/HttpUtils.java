package com.example.jjx.util;


import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

import com.example.jjx.api.Api;
import com.example.jjx.model.GoodsSkuResponse;
import com.google.gson.Gson;

/**
 * HTTP网络请求工具类
 *
 * <AUTHOR>
 * @Date 2025/7/31 14:43
 * @Description: 封装OkHttp的网络请求工具，支持GET/POST请求和业务接口调用
 */
public class HttpUtils {
    private static final String TAG = "HttpUtils";
    private static HttpUtils instance;
    private final OkHttpClient client;
    private final Handler mainHandler; // 用于主线程回调

    // 单例模式（双重校验锁，确保线程安全）
    private HttpUtils() {
        // 初始化 OkHttpClient，配置超时时间
        client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // 连接超时
                .readTimeout(10, TimeUnit.SECONDS)    // 读取超时
                .writeTimeout(10, TimeUnit.SECONDS)   // 写入超时
                .build();

        // 初始化主线程 Handler
        mainHandler = new Handler(Looper.getMainLooper());
    }

    public static HttpUtils getInstance() {
        if (instance == null) {
            synchronized (HttpUtils.class) {
                if (instance == null) {
                    instance = new HttpUtils();
                }
            }
        }
        return instance;
    }

    /**
     * GET 请求
     *
     * @param url      接口地址
     * @param params   请求参数（可选）
     * @param callback 回调接口（主线程执行）
     */
    public void get(String url, Map<String, String> params, final HttpCallback callback) {
        // 拼接参数到 URL
        if (params != null && !params.isEmpty()) {
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey())
                        .append("=")
                        .append(entry.getValue())
                        .append("&");
            }
            // 移除最后一个 "&"
            url = urlBuilder.substring(0, urlBuilder.length() - 1);
        }

        // 创建 GET 请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("User-Agent", "Android HttpUtils")
                .build();

        // 执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, final IOException e) {
                Log.e(TAG, "GET 请求失败：" + e.getMessage());
                // 切换到主线程回调错误
                mainHandler.post(() -> callback.onFailure("网络请求失败：" + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    final String responseData = response.body().string();
                    Log.d(TAG, "GET 响应数据：" + responseData);
                    // 切换到主线程回调成功
                    mainHandler.post(() -> callback.onSuccess(responseData));
                } else {
                    final String errorMsg = "请求失败，状态码：" + response.code();
                    Log.e(TAG, errorMsg);
                    mainHandler.post(() -> callback.onFailure(errorMsg));
                }
                response.close(); // 关闭响应流
            }
        });
    }

    /**
     * POST 请求（表单提交）
     *
     * @param url      接口地址
     * @param params   请求参数（键值对）
     * @param callback 回调接口（主线程执行）
     */
    public void post(String url, Map<String, String> params, final HttpCallback callback) {
        // 构建表单参数
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                formBuilder.add(entry.getKey(), entry.getValue());
            }
        }
        RequestBody requestBody = formBuilder.build();

        // 创建 POST 请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(requestBody)
                .build();

        // 执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, final IOException e) {
                Log.e(TAG, "POST 请求失败：" + e.getMessage());
                mainHandler.post(() -> callback.onFailure("网络请求失败：" + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    final String responseData = response.body().string();
                    Log.d(TAG, "POST 响应数据：" + responseData);
                    mainHandler.post(() -> callback.onSuccess(responseData));
                } else {
                    final String errorMsg = "请求失败，状态码：" + response.code();
                    Log.e(TAG, errorMsg);
                    mainHandler.post(() -> callback.onFailure(errorMsg));
                }
                response.close();
            }
        });
    }

    // ==================== 业务接口封装 ====================

    /**
     * 查询商品SKU信息
     *
     * @param barcode 商品69码
     * @param callback 业务回调接口
     */
    public void checkGoodsSku(String barcode, GoodsSkuCallback callback) {
        Map<String, String> params = new HashMap<>();
        params.put("barcode", barcode);

        post(Api.CheckGoodsSku, params, new HttpCallback() {
            @Override
            public void onSuccess(String response) {
                try {
                    Log.d(TAG, "商品查询API响应：" + response);
                    parseGoodsSkuResponse(response, callback);
                } catch (Exception e) {
                    Log.e(TAG, "商品信息解析失败", e);
                    callback.onParseError("数据解析失败：" + e.getMessage());
                }
            }

            @Override
            public void onFailure(String errorMsg) {
                Log.e(TAG, "商品查询API失败：" + errorMsg);
                callback.onNetworkError(errorMsg);
            }
        });
    }

    /**
     * 解析商品SKU查询响应
     *
     * @param response JSON响应字符串
     * @param callback 业务回调接口
     */
    private void parseGoodsSkuResponse(String response, GoodsSkuCallback callback) {
        try {
            Gson gson = new Gson();
            GoodsSkuResponse skuResponse = gson.fromJson(response, GoodsSkuResponse.class);

            if (skuResponse == null) {
                callback.onParseError("响应数据为空");
                return;
            }

            if (!"success".equals(skuResponse.getMsg()) || skuResponse.getCode() != 200) {
                callback.onBusinessError(skuResponse.getMsg(), skuResponse.getCode());
                return;
            }

            GoodsSkuResponse.GoodsInfo goods = skuResponse.getGoods();
            if (goods == null) {
                callback.onGoodsNotFound("未找到商品信息");
                return;
            }

            // 成功获取商品信息
            callback.onGoodsFound(goods);

        } catch (Exception e) {
            Log.e(TAG, "JSON解析异常", e);
            callback.onParseError("JSON解析失败：" + e.getMessage());
        }
    }

    // ==================== 回调接口定义 ====================

    /**
     * HTTP请求回调接口（主线程执行）
     */
    public interface HttpCallback {
        // 请求成功（返回原始响应字符串）
        void onSuccess(String response);

        // 请求失败（返回错误信息）
        void onFailure(String errorMsg);
    }

    /**
     * 商品SKU查询业务回调接口（主线程执行）
     */
    public interface GoodsSkuCallback {
        /**
         * 成功找到商品信息
         * @param goods 商品信息对象
         */
        void onGoodsFound(GoodsSkuResponse.GoodsInfo goods);

        /**
         * 未找到商品信息
         * @param message 提示信息
         */
        void onGoodsNotFound(String message);

        /**
         * 业务逻辑错误（如API返回错误码）
         * @param message 错误信息
         * @param code 错误码
         */
        void onBusinessError(String message, int code);

        /**
         * 网络请求失败
         * @param errorMsg 网络错误信息
         */
        void onNetworkError(String errorMsg);

        /**
         * 数据解析失败
         * @param errorMsg 解析错误信息
         */
        void onParseError(String errorMsg);
    }
}
