package com.example.jjx.util;


import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * <AUTHOR>
 * @Date 2025/7/31 14:43
 * @Description: TODO
 */
public class HttpUtils {
    private static final String TAG = "HttpUtils";
    private static HttpUtils instance;
    private final OkHttpClient client;
    private final Handler mainHandler; // 用于主线程回调

    // 单例模式（双重校验锁，确保线程安全）
    private HttpUtils() {
        // 初始化 OkHttpClient，配置超时时间
        client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS) // 连接超时
                .readTimeout(10, TimeUnit.SECONDS)    // 读取超时
                .writeTimeout(10, TimeUnit.SECONDS)   // 写入超时
                .build();

        // 初始化主线程 Handler
        mainHandler = new Handler(Looper.getMainLooper());
    }

    public static HttpUtils getInstance() {
        if (instance == null) {
            synchronized (HttpUtils.class) {
                if (instance == null) {
                    instance = new HttpUtils();
                }
            }
        }
        return instance;
    }

    /**
     * GET 请求
     *
     * @param url      接口地址
     * @param params   请求参数（可选）
     * @param callback 回调接口（主线程执行）
     */
    public void get(String url, Map<String, String> params, final HttpCallback callback) {
        // 拼接参数到 URL
        if (params != null && !params.isEmpty()) {
            StringBuilder urlBuilder = new StringBuilder(url);
            urlBuilder.append("?");
            for (Map.Entry<String, String> entry : params.entrySet()) {
                urlBuilder.append(entry.getKey())
                        .append("=")
                        .append(entry.getValue())
                        .append("&");
            }
            // 移除最后一个 "&"
            url = urlBuilder.substring(0, urlBuilder.length() - 1);
        }

        // 创建 GET 请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .addHeader("User-Agent", "Android HttpUtils")
                .build();

        // 执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, final IOException e) {
                Log.e(TAG, "GET 请求失败：" + e.getMessage());
                // 切换到主线程回调错误
                mainHandler.post(() -> callback.onFailure("网络请求失败：" + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    final String responseData = response.body().string();
                    Log.d(TAG, "GET 响应数据：" + responseData);
                    // 切换到主线程回调成功
                    mainHandler.post(() -> callback.onSuccess(responseData));
                } else {
                    final String errorMsg = "请求失败，状态码：" + response.code();
                    Log.e(TAG, errorMsg);
                    mainHandler.post(() -> callback.onFailure(errorMsg));
                }
                response.close(); // 关闭响应流
            }
        });
    }

    /**
     * POST 请求（表单提交）
     *
     * @param url      接口地址
     * @param params   请求参数（键值对）
     * @param callback 回调接口（主线程执行）
     */
    public void post(String url, Map<String, String> params, final HttpCallback callback) {
        // 构建表单参数
        FormBody.Builder formBuilder = new FormBody.Builder();
        if (params != null && !params.isEmpty()) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                formBuilder.add(entry.getKey(), entry.getValue());
            }
        }
        RequestBody requestBody = formBuilder.build();

        // 创建 POST 请求
        Request request = new Request.Builder()
                .url(url)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(requestBody)
                .build();

        // 执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, final IOException e) {
                Log.e(TAG, "POST 请求失败：" + e.getMessage());
                mainHandler.post(() -> callback.onFailure("网络请求失败：" + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (response.isSuccessful()) {
                    final String responseData = response.body().string();
                    Log.d(TAG, "POST 响应数据：" + responseData);
                    mainHandler.post(() -> callback.onSuccess(responseData));
                } else {
                    final String errorMsg = "请求失败，状态码：" + response.code();
                    Log.e(TAG, errorMsg);
                    mainHandler.post(() -> callback.onFailure(errorMsg));
                }
                response.close();
            }
        });
    }

    /**
     * 回调接口（主线程执行）
     */
    public interface HttpCallback {
        // 请求成功（返回原始响应字符串）
        void onSuccess(String response);

        // 请求失败（返回错误信息）
        void onFailure(String errorMsg);
    }
}
