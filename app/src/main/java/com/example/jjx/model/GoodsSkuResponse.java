package com.example.jjx.model;

/**
 * <AUTHOR>
 * @Date 2025/7/31 15:30
 * @Description: 商品SKU查询响应数据模型
 */
public class GoodsSkuResponse {
    private String msg;
    private int code;
    private GoodsInfo goods;

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public GoodsInfo getGoods() {
        return goods;
    }

    public void setGoods(GoodsInfo goods) {
        this.goods = goods;
    }

    /**
     * 商品信息内部类
     */
    public static class GoodsInfo {
        private int id;
        private String code;
        private String name;
        private String productSpecs;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getProductSpecs() {
            return productSpecs;
        }

        public void setProductSpecs(String productSpecs) {
            this.productSpecs = productSpecs;
        }
    }
}
