package com.example.jjx.api;

import android.widget.Toast;
import com.example.jjx.activity.MainActivity;
import com.example.jjx.util.HttpUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2025/7/31 14:38
 * @Description: TODO
 */
public class Api {
    static final String ip = "http://*************";


    static final String port = "80";


    public void checkGoodsSku() {

    }

    public static final String CheckGoodsSku = ip + ":" + port + "/union/3024/pda/CheckGoodsSku";

}
