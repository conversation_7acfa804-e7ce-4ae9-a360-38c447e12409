package com.example.jjx.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.example.jjx.R;
import com.example.jjx.databinding.ActivityMainBinding;
import com.example.jjx.util.DialogUtil;

public class MainActivity extends AppCompatActivity {

    private ActivityMainBinding binding;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化视图绑定
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setSupportActionBar(binding.toolbar);

        // 初始化所有按钮点击事件
        initButtonListeners();
    }

    /**
     * 初始化所有功能按钮的点击事件
     */
    private void initButtonListeners() {
        // 装盒按钮点击事件
        binding.btnPackBox.setOnClickListener(v -> {
            // 正确的调用方式
//            DialogUtil.showAlertDialog(this, "操作成功", (dialog, which) -> {
//                // 处理点击事件
//                dialog.dismiss();
//            });
            //DialogUtil.showAlertDialog("装盒");
            navigateToActivity(PackBoxActivity.class, "装盒");
        });
        // 拆盒按钮点击事件
        binding.btnUnpackBox.setOnClickListener(v -> {
//            navigateToActivity(BoxUnpackingActivity.class, "拆盒");
        });
        // 组箱按钮点击事件
        binding.btnPackCase.setOnClickListener(v -> {
//            navigateToActivity(BoxGroupingActivity.class, "组箱");
        });
        // 拆箱按钮点击事件
        binding.btnUnpackCase.setOnClickListener(v -> {
//            navigateToActivity(CartonUnpackingActivity.class, "拆箱");
        });

        // 出库按钮点击事件
        binding.btnOutStock.setOnClickListener(v -> {
//            navigateToActivity(OutboundActivity.class, "出库");
        });

        // 退货按钮点击事件
        binding.btnReturnStock.setOnClickListener(v -> {
//            navigateToActivity(ReturnActivity.class, "退货");
        });

        // 单品装箱按钮点击事件
        binding.btnSinglePack.setOnClickListener(v -> {
//            navigateToActivity(SingleProductPackingActivity.class, "单品装箱");
        });

        // 一键成箱按钮点击事件
        binding.btnOneClick.setOnClickListener(v -> {
//            navigateToActivity(OneClickPackingActivity.class, "一键成箱");
        });

        // 更换经销商按钮点击事件
        binding.btnChangeAgent.setOnClickListener(v -> {
//            navigateToActivity(ChangeDealerActivity.class, "更换经销商");
        });

        // 用户登出按钮点击事件
        binding.btnLogout.setOnClickListener(v -> {
//            navigateToActivity(ChangeDealerActivity.class, "更换经销商");
        });

        // 扫码查询按钮点击事件
        binding.btnScanQuery.setOnClickListener(v -> {
//            navigateToActivity(ChangeDealerActivity.class, "更换经销商");
        });

        // 更新软件版本点击事件
        binding.btnUpdate.setOnClickListener(v -> {
//            navigateToActivity(ChangeDealerActivity.class, "更换经销商");
        });
    }

    /**
     * 跳转到指定Activity并显示提示
     *
     * @param targetActivity 目标Activity类
     * @param functionName   功能名称（用于提示）
     */
    private void navigateToActivity(Class<?> targetActivity, String functionName) {
        // 显示功能提示
        Toast.makeText(this, "进入" + functionName + "功能", Toast.LENGTH_SHORT).show();
        // 跳转到对应功能页面
        Intent intent = new Intent(this, targetActivity);
        startActivity(intent);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // 加载菜单资源（如果有）
        getMenuInflater().inflate(R.menu.menu_main, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        return super.onOptionsItemSelected(item);
    }
}
