package com.example.jjx.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;

import java.util.ArrayList;
import java.util.List;

import com.example.jjx.R;

/**
 * 扫描瓶码页面
 * 
 * <AUTHOR>
 * @Date 2025/7/31 16:00
 * @Description: 扫描瓶码，显示应扫/实扫数量和瓶码列表
 */
public class ScanBottleCodeActivity extends AppCompatActivity {

    // ==================== UI控件 ====================
    private TextView tvBoxCode;          // 盒码显示
    private EditText etBottleCode;       // 瓶码输入框
    private TextView tvScanProgress;     // 扫描进度显示 (0/2)
    private ListView lvBottleList;       // 瓶码列表
    private Button btnBack;              // 返回首页按钮
    private Button btnComplete;          // 完成按钮
    
    // ==================== 数据状态 ====================
    private String goodsCode;            // 商品69码
    private String goodsSku;             // 商品SKU
    private String boxRatio;             // 瓶盒比例
    private String boxCode;              // 盒码
    private int expectedCount;           // 应扫数量
    private int actualCount = 0;         // 实扫数量
    private List<String> scannedBottles; // 已扫描的瓶码列表
    private ArrayAdapter<String> bottleAdapter; // 列表适配器

    // ==================== 生命周期方法 ====================
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan_bottle_code);
        
        // 获取传递的数据
        getIntentData();
        
        initViews();
        setupListeners();
        initData();
    }

    // ==================== 初始化方法 ====================
    /**
     * 获取Intent传递的数据
     */
    private void getIntentData() {
        Intent intent = getIntent();
        goodsCode = intent.getStringExtra("goodsCode");
        goodsSku = intent.getStringExtra("goodsSku");
        boxRatio = intent.getStringExtra("boxRatio");
        boxCode = intent.getStringExtra("boxCode");
        
        // 解析应扫数量
        try {
            expectedCount = Integer.parseInt(boxRatio);
        } catch (NumberFormatException e) {
            expectedCount = 1; // 默认值
        }
    }

    /**
     * 初始化UI控件
     */
    private void initViews() {
        tvBoxCode = findViewById(R.id.tv_box_code);
        etBottleCode = findViewById(R.id.et_bottle_code);
        tvScanProgress = findViewById(R.id.tv_scan_progress);
        lvBottleList = findViewById(R.id.lv_bottle_list);
        btnBack = findViewById(R.id.btn_back);
        btnComplete = findViewById(R.id.btn_complete);
        
        // 设置默认焦点
        etBottleCode.requestFocus();
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 瓶码输入框回车事件
        etBottleCode.setOnEditorActionListener(this::onBottleCodeEnterPressed);
        
        // 返回按钮点击事件
        btnBack.setOnClickListener(v -> finish());
        
        // 完成按钮点击事件
        btnComplete.setOnClickListener(v -> completeScanning());
    }

    /**
     * 初始化数据
     */
    private void initData() {
        // 显示盒码
        tvBoxCode.setText("盒号: " + boxCode);
        
        // 初始化瓶码列表
        scannedBottles = new ArrayList<>();
        bottleAdapter = new ArrayAdapter<>(this, android.R.layout.simple_list_item_1, scannedBottles);
        lvBottleList.setAdapter(bottleAdapter);
        
        // 更新进度显示
        updateScanProgress();
        
        // 初始状态完成按钮不可用
        btnComplete.setEnabled(false);
    }

    // ==================== 事件处理方法 ====================
    /**
     * 瓶码输入框回车事件处理
     */
    private boolean onBottleCodeEnterPressed(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_DONE || 
            (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER && 
             event.getAction() == KeyEvent.ACTION_DOWN)) {
            handleBottleCodeScan();
            return true;
        }
        return false;
    }

    // ==================== 业务逻辑方法 ====================
    /**
     * 处理瓶码扫描
     */
    private void handleBottleCodeScan() {
        String bottleCode = etBottleCode.getText().toString().trim();
        
        if (bottleCode.isEmpty()) {
            showToast("请扫描瓶码");
            return;
        }
        
        // 检查是否已经扫描过
        if (scannedBottles.contains(bottleCode)) {
            showToast("该瓶码已扫描过");
            etBottleCode.setText("");
            return;
        }
        
        // 检查是否超过应扫数量
        if (actualCount >= expectedCount) {
            showToast("已达到应扫数量，无需继续扫描");
            etBottleCode.setText("");
            return;
        }
        
        // 添加到列表
        scannedBottles.add(bottleCode);
        actualCount++;
        
        // 更新UI
        bottleAdapter.notifyDataSetChanged();
        updateScanProgress();
        etBottleCode.setText("");
        
        showToast("扫描成功");
        
        // 检查是否完成扫描
        if (actualCount >= expectedCount) {
            showToast("扫描完成！");
            btnComplete.setEnabled(true);
        }
    }

    /**
     * 更新扫描进度显示
     */
    private void updateScanProgress() {
        tvScanProgress.setText(actualCount + "/" + expectedCount);
    }

    /**
     * 完成扫描
     */
    private void completeScanning() {
        if (actualCount < expectedCount) {
            showToast("扫描数量不足，请继续扫描");
            return;
        }
        
        // TODO: 这里应该调用API提交装盒数据
        showToast("装盒完成！");
        
        // 返回主页面
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
        finish();
    }

    // ==================== 工具方法 ====================
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
