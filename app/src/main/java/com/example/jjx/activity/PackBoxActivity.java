package com.example.jjx.activity;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;

import java.util.*;

import com.example.jjx.R;
import com.example.jjx.api.Api;
import com.example.jjx.util.HttpUtils;
import com.example.jjx.model.GoodsSkuResponse;
import com.google.gson.Gson;


/**
 * <AUTHOR>
 * @Date 2025/7/31 11:27
 * @Description: TODO
 */
public class PackBoxActivity extends AppCompatActivity {

    private EditText etGoodsCode;    // 商品69码输入框
    private EditText etSku;        // 商品SKU显示框（只读）
    private EditText etBoxRatio;   // 瓶盒比例输入框
    private Button btnBack;        // 返回首页按钮
    private Button btnPack;        // 装盒按钮

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.pack_box);
        // 初始化控件
        etGoodsCode = findViewById(R.id.et_goodsCode);
        etSku = findViewById(R.id.et_sku);
        etBoxRatio = findViewById(R.id.et_box_ratio);
        btnBack = findViewById(R.id.btn_back);
        btnPack = findViewById(R.id.btn_pack);
        etGoodsCode.setFocusable(true);

        // 设置软键盘回车事件监听
        etGoodsCode.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_DONE || (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER && event.getAction() == KeyEvent.ACTION_DOWN)) {
                // 在这里处理回车后的逻辑（例如验证输入、自动填充SKU等）
                handleBarcodeEnter();
                return true;
            }
            return false;
        });

        // SKU文本框设置为只读
        etSku.setFocusable(false);
        etSku.setClickable(false);

        // 返回首页按钮事件
        btnBack.setOnClickListener(v -> {
            finish(); // 关闭当前页面，返回上一级
        });

        // 装盒按钮事件
        btnPack.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 获取输入内容（实际需校验非空、格式等）
                String barcode = etGoodsCode.getText().toString().trim();
                String sku = etSku.getText().toString().trim();
                String ratio = etBoxRatio.getText().toString().trim();

                // 校验必填项
                if (barcode.isEmpty()) {
                    Toast.makeText(PackBoxActivity.this, "请先扫描商品69码", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (sku.isEmpty()) {
                    Toast.makeText(PackBoxActivity.this, "请先扫描69码获取商品信息", Toast.LENGTH_SHORT).show();
                    return;
                }
                if (ratio.isEmpty()) {
                    Toast.makeText(PackBoxActivity.this, "请输入瓶盒比例", Toast.LENGTH_SHORT).show();
                    return;
                }

                // 模拟装盒逻辑（实际需对接业务接口）
                Toast.makeText(PackBoxActivity.this, "装盒请求：\n条码：" + barcode + "\nSKU：" + sku + "\n比例：1:" + ratio, Toast.LENGTH_SHORT).show();
            }
        });
    }

    // 回车后执行的逻辑
    private void handleBarcodeEnter() {
        String barcode = etGoodsCode.getText().toString().trim();

        // 1. 简单校验
        if (barcode.isEmpty()) {
            Toast.makeText(this, "请输入商品69码", Toast.LENGTH_SHORT).show();
            return;
        }

        // 2. 调用API查询商品信息
        Toast.makeText(this, "正在查询商品信息...", Toast.LENGTH_SHORT).show();
        CheckGoodsSku(barcode);
    }

    // 查询商品SKU信息
    public void CheckGoodsSku(String etGoodsCode) {
        Map<String, String> params = new HashMap<>();
        params.put("goodsSku", etGoodsCode);
        HttpUtils.getInstance().post(Api.CheckGoodsSku, params, new HttpUtils.HttpCallback() {
            @Override
            public void onSuccess(String response) {
                System.out.println("API响应：" + response);
                parseGoodsSkuResponse(response);
            }

            @Override
            public void onFailure(String errorMsg) {
                System.out.println("API错误：" + errorMsg);
                Toast.makeText(PackBoxActivity.this, "查询失败：" + errorMsg, Toast.LENGTH_SHORT).show();
                // 清空SKU和比例信息
                etSku.setText("");
                etBoxRatio.setText("");
            }
        });
    }

    /**
     * 解析商品SKU查询响应
     *
     * @param response API返回的JSON字符串
     */
    private void parseGoodsSkuResponse(String response) {
        try {
            Gson gson = new Gson();
            GoodsSkuResponse skuResponse = gson.fromJson(response, GoodsSkuResponse.class);

            if (skuResponse != null && "success".equals(skuResponse.getMsg()) && skuResponse.getCode() == 200) {
                GoodsSkuResponse.GoodsInfo goods = skuResponse.getGoods();
                if (goods != null) {
                    // 自动填充SKU名称
                    etSku.setText(goods.getName());

                    // 自动填充包装比例（从productSpecs字段获取）
                    String productSpecs = goods.getProductSpecs();
                    if (productSpecs != null && !productSpecs.isEmpty()) {
                        etBoxRatio.setText(productSpecs);
                    }

                    Toast.makeText(this, "商品信息获取成功", Toast.LENGTH_SHORT).show();

                    // 自动聚焦到瓶盒比例输入框（如果比例为空）
                    if (productSpecs == null || productSpecs.isEmpty()) {
                        etBoxRatio.requestFocus();
                    }
                } else {
                    Toast.makeText(this, "未找到商品信息", Toast.LENGTH_SHORT).show();
                    etSku.setText("");
                    etBoxRatio.setText("");
                }
            } else {
                String errorMsg = skuResponse != null ? skuResponse.getMsg() : "未知错误";
                Toast.makeText(this, "查询失败：" + errorMsg, Toast.LENGTH_SHORT).show();
                etSku.setText("");
                etBoxRatio.setText("");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "数据解析失败：" + e.getMessage(), Toast.LENGTH_SHORT).show();
            etSku.setText("");
            etBoxRatio.setText("");
        }
    }
}