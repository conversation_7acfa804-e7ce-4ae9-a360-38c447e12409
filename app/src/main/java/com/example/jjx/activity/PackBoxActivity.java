package com.example.jjx.activity;

import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;

import com.example.jjx.R;
import com.example.jjx.util.HttpUtils;
import com.example.jjx.model.GoodsSkuResponse;

/**
 * 装盒功能页面
 * 
 * <AUTHOR>
 * @Date 2025/7/31 11:27
 * @Description: 商品装盒操作页面，支持扫描69码自动获取商品信息
 */
public class PackBoxActivity extends AppCompatActivity {

    // ==================== UI控件 ====================
    private EditText etGoodsCode;    // 商品69码输入框
    private EditText etSku;          // 商品SKU显示框（只读）
    private EditText etBoxRatio;     // 瓶盒比例输入框
    private Button btnBack;          // 返回首页按钮
    private Button btnPack;          // 装盒按钮
    
    // ==================== 数据状态 ====================
    private String lastValidatedBarcode = "";  // 最后一次验证成功的69码

    // ==================== 生命周期方法 ====================
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.pack_box);
        
        initViews();
        setupListeners();
    }

    // ==================== 初始化方法 ====================
    /**
     * 初始化UI控件
     */
    private void initViews() {
        etGoodsCode = findViewById(R.id.et_goodsCode);
        etSku = findViewById(R.id.et_sku);
        etBoxRatio = findViewById(R.id.et_box_ratio);
        btnBack = findViewById(R.id.btn_back);
        btnPack = findViewById(R.id.btn_pack);
        
        // 设置SKU文本框为只读
        etSku.setFocusable(false);
        etSku.setClickable(false);

        // 设置69码输入框为默认焦点
        etGoodsCode.requestFocus();
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 69码输入框回车事件
        etGoodsCode.setOnEditorActionListener(this::onBarcodeEnterPressed);

        // 69码文本变化监听
        etGoodsCode.addTextChangedListener(new BarcodeTextWatcher());
        
        // 返回按钮点击事件
        btnBack.setOnClickListener(v -> finish());
        
        // 装盒按钮点击事件
        btnPack.setOnClickListener(this::onPackButtonClicked);
    }

    // ==================== 事件处理方法 ====================
    /**
     * 69码输入框回车事件处理
     */
    private boolean onBarcodeEnterPressed(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_DONE || 
            (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER && 
             event.getAction() == KeyEvent.ACTION_DOWN)) {
            handleBarcodeValidation();
            return true;
        }
        return false;
    }

    /**
     * 装盒按钮点击事件处理
     */
    private void onPackButtonClicked(android.view.View v) {
        if (validateInputs()) {
            performPackingOperation();
        }
    }

    // ==================== 业务逻辑方法 ====================
    /**
     * 处理69码验证
     */
    private void handleBarcodeValidation() {
        String barcode = etGoodsCode.getText().toString().trim();
        
        if (barcode.isEmpty()) {
            showToast("请输入商品69码");
            return;
        }
        
        showToast("正在查询商品信息...");
        queryGoodsInfo(barcode);
    }

    /**
     * 验证输入数据
     */
    private boolean validateInputs() {
        String barcode = etGoodsCode.getText().toString().trim();
        String sku = etSku.getText().toString().trim();
        String ratio = etBoxRatio.getText().toString().trim();
        
        if (barcode.isEmpty()) {
            showToast("请先扫描商品69码");
            return false;
        }
        
        if (sku.isEmpty()) {
            showToast("请先扫描69码获取商品信息");
            return false;
        }
        
        if (ratio.isEmpty()) {
            showToast("请输入瓶盒比例");
            return false;
        }
        
        // 检查69码是否与已验证的69码一致
        if (!barcode.equals(lastValidatedBarcode)) {
            showToast("69码已修改，请重新扫描验证商品信息");
            return false;
        }
        
        return true;
    }

    /**
     * 执行装盒操作
     */
    private void performPackingOperation() {
        String barcode = etGoodsCode.getText().toString().trim();
        String sku = etSku.getText().toString().trim();
        String ratio = etBoxRatio.getText().toString().trim();
        
        // TODO: 实际的装盒业务逻辑
        showToast("装盒请求：\n条码：" + barcode + "\nSKU：" + sku + "\n比例：1:" + ratio);
    }

    // ==================== 网络请求方法 ====================
    /**
     * 查询商品信息
     */
    private void queryGoodsInfo(String barcode) {
        HttpUtils.getInstance().checkGoodsSku(barcode, new HttpUtils.GoodsSkuCallback() {
            @Override
            public void onGoodsFound(GoodsSkuResponse.GoodsInfo goods) {
                fillGoodsInfo(goods);
            }

            @Override
            public void onGoodsNotFound(String message) {
                showToast(message);
                clearGoodsInfo();
            }

            @Override
            public void onBusinessError(String message, int code) {
                showToast("查询失败：" + message + " (错误码:" + code + ")");
                clearGoodsInfo();
            }

            @Override
            public void onNetworkError(String errorMsg) {
                showToast("网络请求失败：" + errorMsg);
                clearGoodsInfo();
            }

            @Override
            public void onParseError(String errorMsg) {
                showToast("数据解析失败：" + errorMsg);
                clearGoodsInfo();
            }
        });
    }



    // ==================== 数据处理方法 ====================

    /**
     * 填充商品信息
     */
    private void fillGoodsInfo(GoodsSkuResponse.GoodsInfo goods) {
        etSku.setText(goods.getName());
        
        String productSpecs = goods.getProductSpecs();
        if (productSpecs != null && !productSpecs.isEmpty()) {
            etBoxRatio.setText(productSpecs);
        }
        
        // 记录验证成功的69码
        lastValidatedBarcode = etGoodsCode.getText().toString().trim();
        
        showToast("商品信息获取成功");
        
        // 如果比例为空，聚焦到比例输入框
        if (productSpecs == null || productSpecs.isEmpty()) {
            etBoxRatio.requestFocus();
        }
    }



    /**
     * 清空商品信息
     */
    private void clearGoodsInfo() {
        etSku.setText("");
        etBoxRatio.setText("");
        lastValidatedBarcode = "";
    }

    // ==================== 工具方法 ====================
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    // ==================== 内部类 ====================
    /**
     * 69码文本变化监听器
     */
    private class BarcodeTextWatcher implements TextWatcher {
        @Override
        public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            // 文本变化前
        }

        @Override
        public void onTextChanged(CharSequence s, int start, int before, int count) {
            String currentBarcode = s.toString().trim();
            // 如果当前69码与最后验证成功的69码不同，清空商品信息
            if (!currentBarcode.equals(lastValidatedBarcode)) {
                etSku.setText("");
                etBoxRatio.setText("");
                if (currentBarcode.isEmpty()) {
                    lastValidatedBarcode = "";
                }
            }
        }

        @Override
        public void afterTextChanged(Editable s) {
            // 文本变化后
        }
    }
}
