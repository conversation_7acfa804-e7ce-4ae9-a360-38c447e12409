package com.example.jjx.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.KeyEvent;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.appcompat.app.AppCompatActivity;

import com.example.jjx.R;

/**
 * 扫描盒码页面
 * 
 * <AUTHOR>
 * @Date 2025/7/31 16:00
 * @Description: 扫描盒码并校验，校验通过后进入扫描瓶码页面
 */
public class ScanBoxCodeActivity extends AppCompatActivity {

    // ==================== UI控件 ====================
    private EditText etBoxCode;      // 盒码输入框
    private Button btnBack;          // 返回首页按钮
    
    // ==================== 数据状态 ====================
    private String goodsCode;        // 商品69码
    private String goodsSku;         // 商品SKU
    private String boxRatio;         // 瓶盒比例

    // ==================== 生命周期方法 ====================
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_scan_box_code);
        
        // 获取传递的商品信息
        getIntentData();
        
        initViews();
        setupListeners();
    }

    // ==================== 初始化方法 ====================
    /**
     * 获取Intent传递的数据
     */
    private void getIntentData() {
        Intent intent = getIntent();
        goodsCode = intent.getStringExtra("goodsCode");
        goodsSku = intent.getStringExtra("goodsSku");
        boxRatio = intent.getStringExtra("boxRatio");
    }

    /**
     * 初始化UI控件
     */
    private void initViews() {
        etBoxCode = findViewById(R.id.et_box_code);
        btnBack = findViewById(R.id.btn_back);
        
        // 设置默认焦点
        etBoxCode.requestFocus();
    }

    /**
     * 设置事件监听器
     */
    private void setupListeners() {
        // 盒码输入框回车事件
        etBoxCode.setOnEditorActionListener(this::onBoxCodeEnterPressed);
        
        // 返回按钮点击事件
        btnBack.setOnClickListener(v -> finish());
    }

    // ==================== 事件处理方法 ====================
    /**
     * 盒码输入框回车事件处理
     */
    private boolean onBoxCodeEnterPressed(TextView v, int actionId, KeyEvent event) {
        if (actionId == EditorInfo.IME_ACTION_DONE || 
            (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER && 
             event.getAction() == KeyEvent.ACTION_DOWN)) {
            handleBoxCodeValidation();
            return true;
        }
        return false;
    }

    // ==================== 业务逻辑方法 ====================
    /**
     * 处理盒码验证
     */
    private void handleBoxCodeValidation() {
        String boxCode = etBoxCode.getText().toString().trim();
        
        if (boxCode.isEmpty()) {
            showToast("请扫描盒码");
            return;
        }
        
        // TODO: 这里应该调用API验证盒码
        // 暂时模拟验证成功
        showToast("盒码验证成功");
        
        // 跳转到扫描瓶码页面
        navigateToScanBottleCode(boxCode);
    }

    /**
     * 跳转到扫描瓶码页面
     */
    private void navigateToScanBottleCode(String boxCode) {
        Intent intent = new Intent(this, ScanBottleCodeActivity.class);
        intent.putExtra("goodsCode", goodsCode);
        intent.putExtra("goodsSku", goodsSku);
        intent.putExtra("boxRatio", boxRatio);
        intent.putExtra("boxCode", boxCode);
        startActivity(intent);
    }

    // ==================== 工具方法 ====================
    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
