<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fitsSystemWindows="true"
        tools:context=".activity.MainActivity">

    <com.google.android.material.appbar.AppBarLayout
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:fitsSystemWindows="true"
            >

        <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"/>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
            xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="16dp"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 第一行按钮 -->
        <Button
                android:id="@+id/btn_pack_box"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginEnd="8dp"
                android:text="装盒"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_unpack_box"
                app:layout_constraintWidth_percent="0.45"/>

        <Button
                android:id="@+id/btn_unpack_box"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginStart="8dp"
                android:text="拆盒"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toEndOf="@+id/btn_pack_box"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_percent="0.45"/>

        <!-- 第二行按钮 -->
        <Button
                android:id="@+id/btn_pack_case"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:text="组箱"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_pack_box"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_unpack_case"
                app:layout_constraintWidth_percent="0.45"/>

        <Button
                android:id="@+id/btn_unpack_case"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginStart="8dp"
                android:text="拆箱"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_unpack_box"
                app:layout_constraintStart_toEndOf="@+id/btn_pack_case"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_percent="0.45"/>

        <!-- 第三行按钮 -->
        <Button
                android:id="@+id/btn_out_stock"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:text="出库"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_pack_case"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_return"
                app:layout_constraintWidth_percent="0.45"/>

        <Button
                android:id="@+id/btn_return_stock"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginStart="8dp"
                android:text="退货"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_unpack_case"
                app:layout_constraintStart_toEndOf="@+id/btn_outbound"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_percent="0.45"/>

        <!-- 第四行按钮 -->
        <Button
                android:id="@+id/btn_single_pack"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="8dp"
                android:text="单品装箱"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_out_stock"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btn_one_click"
                app:layout_constraintWidth_percent="0.45"/>

        <Button
                android:id="@+id/btn_one_click"
                android:layout_width="0dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:layout_marginStart="8dp"
                android:text="一键成箱"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_return_stock"
                app:layout_constraintStart_toEndOf="@+id/btn_single_pack"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintWidth_percent="0.45"/>

        <!-- 第五行单个按钮 -->
        <Button
                android:id="@+id/btn_change_agent"
                android:layout_width="200dp"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:text="更换经销商"
                android:textSize="18sp"
                android:backgroundTint="#D32F2F"
                app:layout_constraintTop_toBottomOf="@+id/btn_single_pack"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        <!-- 底部功能按钮 -->
        <Button
                android:id="@+id/btn_logout"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="32dp"
                android:text="用户登出"
                android:textSize="16sp"
                android:backgroundTint="#BDBDBD"
                android:textColor="#000000"
                app:layout_constraintTop_toBottomOf="@+id/btn_change_agent"/>

        <Button
                android:id="@+id/btn_scan_query"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:text="扫码查询"
                android:textSize="16sp"
                android:backgroundTint="#BDBDBD"
                android:textColor="#000000"
                app:layout_constraintTop_toBottomOf="@+id/btn_logout"/>

        <Button
                android:id="@+id/btn_update"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginTop="16dp"
                android:text="更新软件版本"
                android:textSize="16sp"
                android:backgroundTint="#BDBDBD"
                android:textColor="#000000"
                app:layout_constraintTop_toBottomOf="@+id/btn_scan_query"/>

    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.coordinatorlayout.widget.CoordinatorLayout>