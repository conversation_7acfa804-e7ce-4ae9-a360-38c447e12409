<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp"
            android:gravity="center_horizontal">

        <!-- 标题 -->
        <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="装盒前准备"
                android:textSize="20sp"
                android:textStyle="bold"
                android:layout_marginBottom="24dp"/>

        <!-- 商品69码输入 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="请扫商品69码"
                    android:textSize="16sp"
                    android:layout_marginBottom="4dp"/>

            <EditText
                    android:id="@+id/et_goodsCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="请扫描或输入69码"/> <!-- 预设值 -->
        </LinearLayout>

        <!-- 商品SKU显示（只读文本框） -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="商品SKU"
                    android:textSize="16sp"
                    android:layout_marginBottom="4dp"/>

            <EditText
                    android:id="@+id/et_sku"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="扫描69码后自动显示"
                    android:focusable="false"
                    android:clickable="false"
                    android:background="@android:drawable/edit_text"
                    android:padding="12dp"/>
        </LinearLayout>

        <!-- 瓶盒比例输入 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="24dp">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="请输入瓶盒比例"
                    android:textSize="16sp"
                    android:layout_marginBottom="4dp"/>

            <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="1："
                        android:textSize="16sp"/>

                <EditText
                        android:id="@+id/et_box_ratio"
                        android:layout_width="140dp"
                        android:layout_height="wrap_content"
                        android:hint="请输入"/>
            </LinearLayout>
        </LinearLayout>

        <!-- 功能按钮 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="16dp">

            <Button
                    android:id="@+id/btn_back"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="返回首页"
                    android:background="#CCCCCC"
                    android:textColor="#FFFFFF"
                    android:layout_marginEnd="16dp"/>

            <Button
                    android:id="@+id/btn_pack"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="装盒"
                    android:background="#D92525"
                    android:textColor="#FFFFFF"/>
        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>