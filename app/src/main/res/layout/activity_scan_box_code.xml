<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   android:background="#F5F5F5">

    <!-- 标题栏 -->
    <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="装盒"
            android:textSize="18sp"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:gravity="center"
            android:background="#4A90E2"
            app:layout_constraintTop_toTopOf="parent"
            xmlns:app="http://schemas.android.com/apk/res-auto"/>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:padding="24dp"
            android:gravity="center_horizontal"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="parent">

        <!-- 请扫盒码提示 -->
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="请扫盒码:"
                android:textSize="16sp"
                android:textColor="#333333"
                android:layout_marginTop="80dp"
                android:layout_marginBottom="16dp"/>

        <!-- 盒码输入框 -->
        <EditText
                android:id="@+id/et_box_code"
                android:layout_width="280dp"
                android:layout_height="48dp"
                android:hint="请扫描盒码"
                android:textSize="16sp"
                android:padding="12dp"
                android:background="@drawable/edit_text_border"
                android:inputType="text"
                android:imeOptions="actionDone"/>

        <!-- 占位空间 -->
        <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"/>

        <!-- 返回首页按钮 -->
        <Button
                android:id="@+id/btn_back"
                android:layout_width="200dp"
                android:layout_height="48dp"
                android:text="返回首页"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:background="#D32F2F"
                android:layout_marginBottom="32dp"/>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
