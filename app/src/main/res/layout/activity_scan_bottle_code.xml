<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
                                                   xmlns:app="http://schemas.android.com/apk/res-auto"
                                                   android:layout_width="match_parent"
                                                   android:layout_height="match_parent"
                                                   android:background="#F5F5F5">

    <!-- 标题栏 -->
    <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:text="装盒"
            android:textSize="18sp"
            android:textColor="#FFFFFF"
            android:textStyle="bold"
            android:gravity="center"
            android:background="#4A90E2"
            app:layout_constraintTop_toTopOf="parent"/>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:orientation="vertical"
            android:padding="16dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title"
            app:layout_constraintBottom_toBottomOf="parent">

        <!-- 盒码显示 -->
        <TextView
                android:id="@+id/tv_box_code"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="盒号: 854242245869370"
                android:textSize="16sp"
                android:textColor="#333333"
                android:padding="12dp"
                android:background="#FFFFFF"
                android:layout_marginBottom="16dp"/>

        <!-- 瓶码输入区域 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="请扫瓶码:"
                    android:textSize="16sp"
                    android:textColor="#333333"
                    android:layout_marginEnd="8dp"/>

            <EditText
                    android:id="@+id/et_bottle_code"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:hint="请扫描瓶码"
                    android:textSize="16sp"
                    android:padding="12dp"
                    android:background="@drawable/edit_text_border"
                    android:inputType="text"
                    android:imeOptions="actionDone"
                    android:layout_marginEnd="8dp"/>

            <!-- 扫描进度 -->
            <TextView
                    android:id="@+id/tv_scan_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0/2"
                    android:textSize="18sp"
                    android:textColor="#FF6600"
                    android:textStyle="bold"/>

        </LinearLayout>

        <!-- 瓶码列表 -->
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="已扫描瓶码:"
                android:textSize="14sp"
                android:textColor="#666666"
                android:layout_marginBottom="8dp"/>

        <ListView
                android:id="@+id/lv_bottle_list"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:background="#FFFFFF"
                android:divider="#E0E0E0"
                android:dividerHeight="1dp"
                android:layout_marginBottom="16dp"/>

        <!-- 底部按钮区域 -->
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center">

            <Button
                    android:id="@+id/btn_back"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="放弃扫码"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#CCCCCC"
                    android:layout_marginEnd="8dp"/>

            <Button
                    android:id="@+id/btn_complete"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:text="完成"
                    android:textSize="16sp"
                    android:textColor="#FFFFFF"
                    android:background="#D32F2F"
                    android:layout_marginStart="8dp"/>

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
